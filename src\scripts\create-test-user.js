const bcrypt = require('bcrypt');
const { pool } = require('../config/db');

async function createTestUser() {
  console.log('Criando usuário de teste...');
  
  try {
    // Verificar se o usuário já existe
    const checkResult = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (checkResult.rows.length > 0) {
      console.log('Usuário de teste já existe.');
      return;
    }
    
    // Criar hash da senha
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    // Inserir usuário
    const result = await pool.query(
      'INSERT INTO users (name, email, password) VALUES ($1, $2, $3) RETURNING id, name, email',
      ['Usuário Teste', '<EMAIL>', hashedPassword]
    );
    
    console.log('Usuário de teste criado com sucesso:', result.rows[0]);
  } catch (error) {
    console.error('Erro ao criar usuário de teste:', error);
  } finally {
    await pool.end();
  }
}

// Executar a função se este arquivo for chamado diretamente
if (require.main === module) {
  createTestUser();
}

module.exports = createTestUser;