:root {
  --primary: #4a6da7;
  --primary-dark: #345089;
  --primary-light: #6b8ac9;
  --secondary: #f5f5f5;
  --text: #333;
  --danger: #dc3545;
  --success: #28a745;
  --warning: #ffc107;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-light) 100%);
  color: var(--text);
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin-bottom: 30px;
}

/* Estilos para autenticação */
.auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
}

.auth-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  padding: 30px;
  width: 100%;
  max-width: 400px;
}

.auth-card h2 {
  margin-bottom: 20px;
  text-align: center;
  color: var(--primary-dark);
}

.auth-links {
  margin-top: 20px;
  text-align: center;
}

.auth-links a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.auth-links a:hover {
  text-decoration: underline;
}

/* Estilos para o dashboard */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.dashboard-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-card h3 {
  color: var(--primary-dark);
  margin-bottom: 15px;
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 10px;
}

/* Estilos para alertas */
#alert-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 100%;
  max-width: 500px;
}

.alert {
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
  animation: fadeIn 0.3s ease-in-out;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Estilos para formulários */
.form-group {
  margin-bottom: 20px;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 109, 167, 0.2);
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: var(--primary-dark);
}

.btn-success {
  background-color: var(--success);
}

.btn-success:hover {
  background-color: #218838;
}

.btn-danger {
  background-color: var(--danger);
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 14px;
}

/* Estilos para navegação */
nav ul {
  display: flex;
  list-style: none;
  align-items: center;
}

nav ul li {
  margin-right: 20px;
}

nav ul li a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.3s;
}

nav ul li a:hover {
  opacity: 0.8;
}

.user-info {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.user-info span {
  margin-right: 10px;
  color: white;
  font-weight: 500;
}

/* Estilos para página de erro */
.error-page {
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 50px auto;
}

.error-page h1 {
  font-size: 80px;
  color: var(--primary);
  margin-bottom: 0;
}

.error-page h2 {
  margin-top: 0;
  color: var(--text);
}

.error-page p {
  margin-bottom: 30px;
  color: #666;
}
