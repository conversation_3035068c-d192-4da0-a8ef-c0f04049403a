const Joi = require('joi');

const taskSchema = Joi.object({
  id: Joi.number().integer(),
  users_id: Joi.number().integer().required(),
  title: Joi.string().min(3).max(100).required(),
  description: Joi.string().allow('', null),
  date_creation: Joi.date().required(),
  date_delivery: Joi.date().required(),
  status: Joi.string().valid('pendente', 'em andamento', 'concluída').required(),
  created_at: Joi.date()
});

module.exports = {
  validate: (task) => taskSchema.validate(task),
  schema: taskSchema
};
