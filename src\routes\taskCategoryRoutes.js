const { Router } = require('express');
const taskCategoryController = require('../controllers/taskCategoryController');
const router = Router();

// Rotas para categorias de tarefas
router.post('/task-categories', taskCategoryController.create);
router.get('/task-categories/user/:userId', taskCategoryController.listByUser);
router.get('/task-categories/:id', taskCategoryController.detail);
router.put('/task-categories/:id', taskCategoryController.update);
router.delete('/task-categories/:id', taskCategoryController.delete);

// Rota para buscar tarefas com categorias
router.get('/tasks-with-categories/user/:userId', taskCategoryController.getTasksWithCategories);

module.exports = router;
