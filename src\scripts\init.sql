-- Ta<PERSON><PERSON> de usuários
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> de tarefas
CREATE TABLE IF NOT EXISTS tasks (
  id SERIAL PRIMARY KEY,
  users_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  date_creation TIMESTAMP NOT NULL,
  date_delivery TIMESTAMP NOT NULL,
  status VARCHAR(20) NOT NULL CHECK (status IN ('pendente', 'em andamento', 'concluída')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de categorias
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ta<PERSON>a de relação entre tarefas e categorias
CREATE TABLE IF NOT EXISTS task_categories (
  task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  category_id INTEGER NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY (task_id, category_id)
);

-- Inserir algumas categorias padrão
INSERT INTO categories (name) 
VALUES ('Trabalho'), ('Estudo'), ('Pessoal'), ('Urgente'), ('Projeto')
ON CONFLICT DO NOTHING;
