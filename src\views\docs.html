<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Documentação - Gerenciador de Tarefas</title>
  <link rel="stylesheet" href="/css/style.css">
  <style>
    .docs-content {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    code {
      background: #f4f4f4;
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    h2 {
      margin-top: 2.2rem;
    }
    
    pre {
      background: #f4f4f4;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>Gerenciador de Tarefas</h1>
      <nav>
        <ul>
          <li><a href="/">Início</a></li>
          <li><a href="/tasks">Tarefas</a></li>
          <li><a href="/docs">Documentação</a></li>
        </ul>
      </nav>
    </div>
  </header>
  
  <main class="container">
    <div class="docs-content">
      <h1>Documentação da API Node.js - Users, Tasks e Categories</h1>

      <p>Esta API expõe endpoints REST para gerenciar usuários, tarefas e categorias. Todos aceitam e retornam JSON.</p>

      <h2>1. Users</h2>
      <ul>
        <li><strong>GET /users</strong> – lista todos os usuários.</li>
        <li><strong>POST /users</strong> – cria um usuário.<br />
          Exemplo de corpo:<br />
          <code>{
      "name": "Ana",
      "email": "<EMAIL>",
      "password": "123456"
    }</code>
        </li>
        <li><strong>GET /users/:id</strong> – detalhes de um usuário pelo ID.</li>
        <li><strong>PUT /users/:id</strong> – atualiza nome e email do usuário.</li>
        <li><strong>DELETE /users/:id</strong> – remove usuário.</li>
        <li><strong>POST /login</strong> – login do usuário.<br />
          Exemplo de corpo:<br />
          <code>{
      "email": "<EMAIL>",
      "password": "123456"
    }</code>
        </li>
      </ul>

      <h2>2. Tasks</h2>
      <ul>
        <li><strong>GET /tasks</strong> – lista todas as tarefas.</li>
        <li><strong>POST /tasks</strong> – cria uma tarefa.<br />
          Exemplo de corpo:<br />
          <code>{
      "users_id": 1,
      "title": "Estudar Node.js",
      "description": "Estudar os módulos e rotas",
      "date_creation": "2025-05-23",
      "date_delivery": "2025-05-30",
      "status": "pendente"
    }</code>
        </li>
        <li><strong>PUT /tasks/:id</strong> – atualiza título, descrição e status da tarefa.</li>
        <li><strong>DELETE /tasks/:id</strong> – exclui tarefa.</li>
      </ul>

      <h2>3. Categories</h2>
      <ul>
        <li><strong>GET /category</strong> – lista todas as categorias.</li>
        <li><strong>POST /category</strong> – cria uma categoria.<br />
          Exemplo de corpo:<br />
          <code>{
      "name": "Trabalho",
      "description": "Tarefas relacionadas ao trabalho"
    }</code>
        </li>
        <li><strong>GET /category/:id</strong> – detalhes da categoria pelo ID.</li>
        <li><strong>PUT /category/:id</strong> – atualiza nome e descrição da categoria.</li>
        <li><strong>DELETE /category/:id</strong> – exclui categoria.</li>
      </ul>

      <h2>Exemplos de uso via <em>curl</em></h2>
      <pre>
curl -X POST http://localhost:3000/users \
     -H "Content-Type: application/json" \
     -d '{"name":"Ana","email":"<EMAIL>","password":"123456"}'

curl http://localhost:3000/task

curl -X POST http://localhost:3000/category \
     -H "Content-Type: application/json" \
     -d '{"name":"Trabalho","description":"Tarefas relacionadas ao trabalho"}'
      </pre>
    </div>
  </main>
</body>
</html>
