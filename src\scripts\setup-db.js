const fs = require('fs');
const path = require('path');
const { pool } = require('../config/db');

async function setupDatabase() {
  console.log('Iniciando configuração do banco de dados no Supabase...');
  
  try {
    // Ler o script SQL
    const sqlPath = path.join(__dirname, 'init.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('Executando script SQL para criar tabelas...');
    
    // Dividir o script em comandos individuais
    const commands = sql.split(';').filter(cmd => cmd.trim() !== '');
    
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i].trim();
      if (command) {
        try {
          await pool.query(command + ';');
          console.log(`Comando ${i+1}/${commands.length} executado com sucesso.`);
        } catch (cmdErr) {
          console.error(`Erro ao executar comando ${i+1}/${commands.length}:`, cmdErr.message);
          console.log('Comando com erro:', command);
          // Continuar com o próximo comando mesmo se houver erro
        }
      }
    }
    
    console.log('Configuração do banco de dados concluída com sucesso!');
  } catch (error) {
    console.error('Erro ao configurar o banco de dados:', error);
  } finally {
    // Fechar o pool de conexões
    await pool.end();
  }
}

// Executar a função se este arquivo for chamado diretamente
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
