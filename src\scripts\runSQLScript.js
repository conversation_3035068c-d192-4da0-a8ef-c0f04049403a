const fs = require('fs');
const path = require('path');
const { pool } = require('../config/db');

const runSQLScript = async () => {
  const filePath = path.join(__dirname, 'init.sql');
  const sql = fs.readFileSync(filePath, 'utf8');

  console.log('Iniciando execução do script SQL no Supabase...');
  console.log('Isso pode levar alguns minutos dependendo da complexidade do script.');

  try {
    // Dividir o script em comandos individuais para melhor gerenciamento de erros
    const commands = sql.split(';').filter(cmd => cmd.trim() !== '');
    
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i].trim();
      if (command) {
        try {
          await pool.query(command + ';');
          console.log(`Comando ${i+1}/${commands.length} executado com sucesso.`);
        } catch (cmdErr) {
          console.error(`Erro ao executar comando ${i+1}/${commands.length}:`, cmdErr.message);
          console.log('Comando com erro:', command);
          // Continuar com o próximo comando mesmo se houver erro
        }
      }
    }
    
    console.log('Script SQL executado com sucesso!');
  } catch (err) {
    console.error('Erro ao executar o script SQL:', err);
  } finally {
    // Não fechar o pool aqui, pois ele será usado em outras partes da aplicação
    console.log('Operação concluída.');
  }
};

// Executar o script se este arquivo for chamado diretamente
if (require.main === module) {
  runSQLScript().then(() => {
    // Fechar o pool apenas se o script for executado diretamente
    pool.end();
  });
} else {
  // Exportar a função para uso em outros arquivos
  module.exports = runSQLScript;
}
