# Teste da rota GET /users
GET http://localhost:3000 HTTP/1.1
Accept: application/json

### Teste da rota GET /users/:id
GET http://localhost:3000/api/users/ HTTP/1.1

### Criar novo usuário
POST http://localhost:3000/api/users
Content-Type: application/json

{
    "name": "<PERSON>", 
    "email": "<EMAIL>", 
    "password": "123456"
}


### Teste da rota GET /users/:id
GET http://localhost:3000/api/users/ HTTP/1.1

### Teste da rota PUT /users/:id
PUT http://localhost:3000/api/users/
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>"
}

### Teste da rota DELETE /users/:id
DELETE http://localhost:3000/api/users/ HTTP/1.1

### Teste da rota POST /login
POST http://localhost:3000/api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "123456"
}

### Teste da rota GET /tasks
GET http://localhost:3000/api/tasks/ HTTP/1.1

### Criar nova tarefa
POST http://localhost:3000/api/tasks
Content-Type: application/json

{
  "users_id": 15,
  "title": "Tarefa de teste",
  "description": "Esta é uma tarefa de teste",
  "date_creation": "2025-06-15T16:13:32.378Z",
  "date_delivery": "2025-06-30",
  "status": "pendente"
}


### Teste da rota PUT /tasks/:id
PUT http://localhost:3000/api/tasks/14 HTTP/1.1
Content-Type: application/json

{
    "users_id": 1,
    "title": "Estudar Node.js Avançado",
    "description": "Estudar os módulos e rotas avançados",
    "date_creation": "2025-05-23",
    "date_delivery": "2025-06-01",
    "status": "em andamento"
}

### Teste da rota DELETE /tasks/:id
DELETE http://localhost:3000/api/tasks/14 HTTP/1.1

### Listar todas as tarefas
GET http://localhost:3000/api/tasks

### Teste da rota GET /category/:id
GET http://localhost:3000/api/category/ HTTP/1.1

### Criar nova categoria
POST http://localhost:3000/api/category 
Content-Type: application/json

{
    "name": "Estudos",
    "description": "Tarefas relacionadas a estudos"
}

### Teste da rota GET /category/:id
GET http://localhost:3000/api/category/:id HTTP/1.1

### Teste da rota PUT /category/:id
PUT http://localhost:3000/api/category/:id
Content-Type: application/json

{
    "name": "Estudos Avançados",
    "description": "Tarefas relacionadas a estudos avançados"
}

### Teste da rota DELETE /category/:id
DELETE http://localhost:3000/api/category/2 HTTP/1.1
