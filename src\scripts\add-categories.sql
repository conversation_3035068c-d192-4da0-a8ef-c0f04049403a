-- Criar tabela de categorias de tarefas
CREATE TABLE IF NOT EXISTS task_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  description TEXT,
  color VARCHAR(7) DEFAULT '#3b82f6', -- Cor em hexadecimal
  users_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(name, users_id) -- Cada usuário pode ter categorias com nomes únicos
);

-- Adicionar coluna category_id na tabela tasks
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES task_categories(id) ON DELETE SET NULL;

-- Inserir algumas categorias padrão (opcional)
-- Estas serão criadas apenas se não existirem
INSERT INTO task_categories (name, description, color, users_id) 
SELECT 'Trabalho', 'Tarefas relacionadas ao trabalho', '#ef4444', id 
FROM users 
WHERE NOT EXISTS (
  SELECT 1 FROM task_categories 
  WHERE name = 'Trabalho' AND users_id = users.id
);

INSERT INTO task_categories (name, description, color, users_id) 
SELECT 'Pessoal', 'Tarefas pessoais e domésticas', '#10b981', id 
FROM users 
WHERE NOT EXISTS (
  SELECT 1 FROM task_categories 
  WHERE name = 'Pessoal' AND users_id = users.id
);

INSERT INTO task_categories (name, description, color, users_id) 
SELECT 'Estudos', 'Tarefas relacionadas aos estudos', '#8b5cf6', id 
FROM users 
WHERE NOT EXISTS (
  SELECT 1 FROM task_categories 
  WHERE name = 'Estudos' AND users_id = users.id
);
