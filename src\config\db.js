const { Pool } = require('pg');
const path = require('path');
const dotenv = require('dotenv');

// Carregar variáveis de ambiente do arquivo .env na raiz do projeto
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

console.log('Configurações de conexão do banco de dados:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_DATABASE:', process.env.DB_DATABASE);
console.log('DB_SSL:', process.env.DB_SSL);

// Configurações específicas para Supabase
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_DATABASE,
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT, 10),
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  // Configurações adicionais para melhorar a estabilidade da conexão
  max: 20, // máximo de conexões no pool
  idleTimeoutMillis: 30000, // tempo máximo que uma conexão pode ficar inativa
  connectionTimeoutMillis: 10000, // tempo máximo para estabelecer uma conexão
});

// Teste de conexão ao iniciar
pool.connect((err, client, release) => {
  if (err) {
    console.error('Erro ao conectar ao banco de dados Supabase:', err.message);
    console.error('Detalhes do erro:', err);
  } else {
    console.log('Conexão com o Supabase estabelecida com sucesso!');
    release();
  }
});

// Adicionar listener para erros de conexão
pool.on('error', (err) => {
  console.error('Erro inesperado no pool de conexões:', err);
});

module.exports = {
  query: (text, params) => pool.query(text, params),
  connect: () => pool.connect(),
  pool: pool, // Exportar o pool para uso em outros lugares se necessário
};
