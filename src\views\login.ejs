<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - Gerenciador de Tarefas</title>
  <link rel="stylesheet" href="/css/style.css">
</head>
<body>
  <main class="container auth-container">
    <div id="alert-container"></div>

    <div class="auth-wrapper">
      <div class="auth-header">
        <h1>Gerenciador de Tarefas</h1>
        <p>Organize suas tarefas de forma simples e eficiente</p>
      </div>

      <div class="auth-tabs">
        <button class="auth-tab active" onclick="showLogin()">Entrar</button>
        <button class="auth-tab" onclick="showRegister()">Cadastrar</button>
      </div>

      <!-- <PERSON><PERSON><PERSON><PERSON> de <PERSON> -->
      <div id="login-form-container" class="auth-card">
        <form id="login-form" onsubmit="loginUser(event)">
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" class="form-control" required placeholder="<EMAIL>">
          </div>

          <div class="form-group">
            <label for="password">Senha</label>
            <input type="password" id="password" name="password" class="form-control" required placeholder="Sua senha">
          </div>

          <div class="form-group">
            <button type="submit" class="btn btn-primary btn-full">Entrar</button>
          </div>
        </form>
      </div>

      <div id="register-form-container" class="auth-card" style="display: none;">
        <form id="register-form" onsubmit="registerUser(event)">
          <div class="form-group">
            <label for="register-name">Nome</label>
            <input type="text" id="register-name" name="name" class="form-control" required placeholder="Seu nome completo">
          </div>

          <div class="form-group">
            <label for="register-email">Email</label>
            <input type="email" id="register-email" name="email" class="form-control" required placeholder="<EMAIL>">
          </div>

          <div class="form-group">
            <label for="register-password">Senha</label>
            <input type="password" id="register-password" name="password" class="form-control" required placeholder="Mínimo 6 caracteres">
          </div>

          <div class="form-group">
            <button type="submit" class="btn btn-success btn-full">Cadastrar</button>
          </div>
        </form>
      </div>
    </div>
  </main>
  
  <script src="/js/auth.js"></script>
  <script>
    function showLogin() {
      document.getElementById('login-form-container').style.display = 'block';
      document.getElementById('register-form-container').style.display = 'none';
      document.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.auth-tab')[0].classList.add('active');
    }

    function showRegister() {
      document.getElementById('login-form-container').style.display = 'none';
      document.getElementById('register-form-container').style.display = 'block';
      document.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.auth-tab')[1].classList.add('active');
    }
  </script>
</body>
</html>